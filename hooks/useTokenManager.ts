"use client";

import { TokenManager } from "@/lib/apiClient";

/**
 * Hook that provides access to token management functionality
 * This is a simple wrapper around the TokenManager for easier use in components
 */
export function useTokenManager() {
  return {
    /**
     * Get the current access token
     */
    getAccessToken: () => TokenManager.getAccessToken(),
    
    /**
     * Get the current refresh token
     */
    getRefreshToken: () => TokenManager.getRefreshToken(),
    
    /**
     * Check if a token is valid (not expired)
     */
    isTokenValid: (token: string) => TokenManager.isTokenValid(token),
    
    /**
     * Check if the current access token is valid
     */
    isAccessTokenValid: () => {
      const token = TokenManager.getAccessToken();
      return token ? TokenManager.isTokenValid(token) : false;
    },
    
    /**
     * Check if the current refresh token is valid
     */
    isRefreshTokenValid: () => {
      const token = TokenManager.getRefreshToken();
      return token ? TokenManager.isTokenValid(token) : false;
    },
    
    /**
     * Check if access token should be refreshed (expires soon)
     */
    shouldRefreshToken: (token: string) => TokenManager.shouldRefreshToken(token),
    
    /**
     * Refresh the access token using the refresh token
     */
    refreshAccessToken: () => TokenManager.refreshAccessToken(),
    
    /**
     * Set both access and refresh tokens
     */
    setTokens: (tokens: { accessToken: string; refreshToken?: string }) => 
      TokenManager.setTokens(tokens),
    
    /**
     * Clear all tokens
     */
    clearTokens: () => TokenManager.clearTokens(),
    
    /**
     * Get both tokens as an object
     */
    getTokens: () => ({
      accessToken: TokenManager.getAccessToken(),
      refreshToken: TokenManager.getRefreshToken()
    }),
    
    /**
     * Check if user is authenticated (has valid access or refresh token)
     */
    isAuthenticated: () => {
      const accessToken = TokenManager.getAccessToken();
      const refreshToken = TokenManager.getRefreshToken();
      
      // User is authenticated if they have a valid access token
      if (accessToken && TokenManager.isTokenValid(accessToken)) {
        return true;
      }
      
      // Or if they have a valid refresh token (can get new access token)
      if (refreshToken && TokenManager.isTokenValid(refreshToken)) {
        return true;
      }
      
      return false;
    }
  };
}
