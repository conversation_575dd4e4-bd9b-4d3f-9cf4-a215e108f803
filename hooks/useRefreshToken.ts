"use client";
import { useEffect, useRef } from "react";
import Cookies from "js-cookie";
import apiClient from "@/lib/apiClient";

interface TokenResponse {
  accessToken: string;
  refreshToken?: string;
}

interface TokenManager {
  getTokens: () => { accessToken: string | undefined; refreshToken: string | undefined };
  setTokens: (tokens: TokenResponse) => void;
  clearTokens: () => void;
  refreshToken: () => Promise<boolean>;
}

// Add a global refresh lock to prevent multiple simultaneous refresh attempts
let isRefreshing = false;
let refreshPromise: Promise<boolean> = Promise.resolve(false);

// Token validation utilities
const TokenUtils = {
  decodeToken: (token: string) => {
    try {
      return JSON.parse(atob(token.split(".")[1]));
    } catch (error) {
      return null;
    }
  },

  isTokenValid: (token: string) => {
    const payload = TokenUtils.decodeToken(token);
    if (!payload) return false;

    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();
    // Consider token valid if it has at least 5 minutes remaining
    return expirationTime - currentTime > 5 * 60 * 1000;
  },

  shouldRefreshToken: (accessToken: string | null) => {
    if (!accessToken) return true;
    
    const payload = TokenUtils.decodeToken(accessToken);
    if (!payload) return true;

    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();

    // Refresh if access token expires in less than 10 minutes
    return expirationTime - currentTime < 10 * 60 * 1000;
  }
};

export function useRefreshToken(): TokenManager {
  const refreshTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const getTokens = () => {
    const accessToken = Cookies.get("accessToken");
    const refreshToken = Cookies.get("refreshToken");
    return { accessToken, refreshToken };
  };

  const setTokens = ({ accessToken, refreshToken }: TokenResponse) => {
    if (accessToken) {
      Cookies.set("accessToken", accessToken, {
        expires: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes
        path: "/",
        sameSite: "Strict",
        secure: window.location.protocol === "https:"
      });
    }

    if (refreshToken) {
      Cookies.set("refreshToken", refreshToken, {
        expires: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days
        path: "/",
        sameSite: "Strict",
        secure: window.location.protocol === "https:"
      });
    }
  };

  const clearTokens = () => {
    Cookies.remove("accessToken", { path: "/" });
    Cookies.remove("refreshToken", { path: "/" });

    // Clear any existing refresh timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
      refreshTimeoutRef.current = null;
    }
  };

  const refreshToken = async (force = false): Promise<boolean> => {
    const { accessToken, refreshToken: currentRefreshToken } = getTokens();

    // If we have a valid access token and refresh isn't forced, don't refresh
    if (!force && accessToken && TokenUtils.isTokenValid(accessToken)) {
      return true;
    }

    // If we're already refreshing, wait for that to complete
    if (isRefreshing) {
      return refreshPromise;
    }

    // If we don't have a refresh token, we can't refresh
    if (!currentRefreshToken) {
      return false;
    }

    isRefreshing = true;

    try {
      refreshPromise = apiClient.post<TokenResponse>("/token", {
        refreshToken: currentRefreshToken,
      })
        .then(({ data }) => {
          if (data.accessToken) {
            setTokens({
              accessToken: data.accessToken,
              refreshToken: currentRefreshToken
            });
            return true;
          } else {
            return false;
          }
        })
        .catch((error: any) => {
          console.error("Token refresh error:", error);

          // If refresh fails with 401/403, clear all tokens
          if (error.response?.status === 401 || error.response?.status === 403) {
            clearTokens();
          }
          return false;
        })
        .finally(() => {
          isRefreshing = false;
          refreshPromise = Promise.resolve(false);
        });

      return refreshPromise;
    } catch (error) {
      console.error("Unexpected error during refresh:", error);
      isRefreshing = false;
      refreshPromise = Promise.resolve(false);
      if (!accessToken || !TokenUtils.isTokenValid(accessToken)) {
        clearTokens();
      }
      return false;
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear any existing refresh timeout on unmount
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
    };
  }, []);

  return {
    getTokens,
    setTokens,
    clearTokens,
    refreshToken: () => refreshToken(true) // Always force refresh when called directly
  };
} 