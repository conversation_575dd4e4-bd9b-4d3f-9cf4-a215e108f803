"use client";

import type { ReactNode } from "react";
import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
} from "react";
import apiClient, { setRefreshTokenInstance } from "@/lib/apiClient";
import { useRefreshToken } from "@/hooks/useRefreshToken";

interface AuthContextType {
  isLoggedIn: boolean;
  isAuthReady: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  user?: {
    group?: string;
    actions?: string[];
  };
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isAuthReady, setIsAuthReady] = useState(false);
  const [user, setUser] = useState<AuthContextType["user"]>();
  const tokenManager = useRefreshToken();
  const isCheckingAuth = useRef(false);
  const initialAuthCheckDone = useRef(false);

  // Register the refresh token function with the API client
  useEffect(() => {
    setRefreshTokenInstance(async () => {
      try {
        const success = await tokenManager.refreshToken();
        return { success };
      } catch (error: any) {
        if (error.response?.status === 403) {
          tokenManager.clearTokens();
        }
        return { success: false, error };
      }
    });
  }, [tokenManager]);

  // Check auth status on mount
  useEffect(() => {
    const checkAuth = async () => {
      if (isCheckingAuth.current) {
        return;
      }

      // Skip if we've already done the initial check
      if (initialAuthCheckDone.current) {
        return;
      }
      // decodeToken: (token: string) => {
      //   try {
      //     return JSON.parse(atob(token.split(".")[1]));
      //   } catch (error) {
      //     return null;
      //   }
      // },

      // isTokenExpired: (token: string): boolean => {
      //   const payload = TokenUtils.decodeToken(token);
      //   if (!payload) return true;

      //   const expirationTime = payload.exp * 1000;
      //   const currentTime = Date.now();
      //   const timeUntilExpiry = expirationTime - currentTime;

      //   // Token is considered expired if less than 5 minutes remaining
      //   return timeUntilExpiry < 5 * 60 * 1000;
      // }
      try {
        isCheckingAuth.current = true;
        setIsAuthReady(false);
        const { accessToken, refreshToken } = tokenManager.getTokens();

        // First check if current access token is valid
        if (accessToken) {
          setIsLoggedIn(true);
          initialAuthCheckDone.current = true;

          // Try to refresh in the background if we have a refresh token
          if (refreshToken) {
            try {
              await tokenManager.refreshToken();
            } catch (error: any) {
              console.error("Background token refresh error:", error);
            }
          }
          return;
        }

        if (refreshToken) {
          try {
            const success = await tokenManager.refreshToken();
            if (success) {
              setIsLoggedIn(true);
              initialAuthCheckDone.current = true;
              return;
            }
          } catch (error: any) {
            if (error.response?.status === 403) {
              tokenManager.clearTokens();
              setIsLoggedIn(false);
              return;
            }
          }
        }

        tokenManager.clearTokens();
        setIsLoggedIn(false);
      } catch (error: any) {
        const { accessToken } = tokenManager.getTokens();
        if (!accessToken) {
          tokenManager.clearTokens();
          setIsLoggedIn(false);
        } else {
          setIsLoggedIn(true);
        }
      } finally {
        setIsAuthReady(true);
        isCheckingAuth.current = false;
        initialAuthCheckDone.current = true;
      }
    };

    checkAuth();
  }, [tokenManager]);

  const login = async (username: string, password: string) => {
    try {
      const response = await apiClient.post("/login", { username, password });

      if (!response.data.accessToken || !response.data.refreshToken) {
        throw new Error("Invalid response format from server");
      }

      tokenManager.setTokens(response.data);

      if (response.data.user) {
        setUser(response.data.user);
      }

      setIsLoggedIn(true);
      initialAuthCheckDone.current = false;
    } catch (error) {
      tokenManager.clearTokens();
      setIsLoggedIn(false);
      throw error;
    } finally {
      setIsAuthReady(true);
    }
  };

  const logout = () => {
    tokenManager.clearTokens();
    setUser(undefined);
    setIsLoggedIn(false);
    initialAuthCheckDone.current = false;
  };

  return (
    <AuthContext.Provider
      value={{ isLoggedIn, isAuthReady, login, logout, user }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
