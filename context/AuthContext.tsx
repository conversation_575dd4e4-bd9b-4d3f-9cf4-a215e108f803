"use client";

import type { ReactNode } from "react";
import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
} from "react";
import apiClient, { TokenManager } from "@/lib/apiClient";

interface AuthContextType {
  isLoggedIn: boolean;
  isAuthReady: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  user?: {
    group?: string;
    actions?: string[];
  };
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isAuthReady, setIsAuthReady] = useState(false);
  const [user, setUser] = useState<AuthContextType["user"]>();
  const isCheckingAuth = useRef(false);
  const initialAuthCheckDone = useRef(false);

  // Check auth status on mount
  useEffect(() => {
    const checkAuth = async () => {
      if (isCheckingAuth.current) {
        return;
      }

      // Skip if we've already done the initial check
      if (initialAuthCheckDone.current) {
        return;
      }

      try {
        isCheckingAuth.current = true;
        setIsAuthReady(false);
        const accessToken = TokenManager.getAccessToken();
        const refreshToken = TokenManager.getRefreshToken();

        // First check if current access token is valid
        if (accessToken && TokenManager.isTokenValid(accessToken)) {
          setIsLoggedIn(true);
          initialAuthCheckDone.current = true;
          return;
        }

        // If access token is invalid but we have a refresh token, try to refresh
        if (refreshToken && TokenManager.isTokenValid(refreshToken)) {
          try {
            const success = await TokenManager.refreshAccessToken();
            if (success) {
              setIsLoggedIn(true);
              initialAuthCheckDone.current = true;
              return;
            }
          } catch (error: any) {
            console.error("Token refresh error during auth check:", error);
          }
        }

        // If we get here, authentication failed
        TokenManager.clearTokens();
        setIsLoggedIn(false);
      } catch (error: any) {
        console.error("Auth check error:", error);
        const accessToken = TokenManager.getAccessToken();
        if (!accessToken || !TokenManager.isTokenValid(accessToken)) {
          TokenManager.clearTokens();
          setIsLoggedIn(false);
        } else {
          setIsLoggedIn(true);
        }
      } finally {
        setIsAuthReady(true);
        isCheckingAuth.current = false;
        initialAuthCheckDone.current = true;
      }
    };

    checkAuth();
  }, []);

  const login = async (username: string, password: string) => {
    try {
      const response = await apiClient.post("/login", { username, password });

      if (!response.data.accessToken || !response.data.refreshToken) {
        throw new Error("Invalid response format from server");
      }

      TokenManager.setTokens(response.data);

      if (response.data.user) {
        setUser(response.data.user);
      }

      setIsLoggedIn(true);
      initialAuthCheckDone.current = false;
    } catch (error) {
      TokenManager.clearTokens();
      setIsLoggedIn(false);
      throw error;
    } finally {
      setIsAuthReady(true);
    }
  };

  const logout = () => {
    TokenManager.clearTokens();
    setUser(undefined);
    setIsLoggedIn(false);
    initialAuthCheckDone.current = false;
  };

  return (
    <AuthContext.Provider
      value={{ isLoggedIn, isAuthReady, login, logout, user }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
