// components/organization-management.tsx
"use client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { SquareAdd, SquarePen } from "@/components/icons/list";
import { Input } from "@/components/ui/input";
import Cookies from "js-cookie";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import apiClient from "@/lib/apiClient";
import { DeleteOrganizationDialog } from "@/app/app/settings/organizations/page";

export interface organization {
  id: string;
  name: string;
  parent_id: string;
  type: string;
  new_organization_name?: string;
  balance: number;
}

// organization Table Component
export function OrganizationTable({
  onDelete,
  organizations,
  setOrganizations,
}: {
  onDelete: (name: string) => void;
  organizations: organization[];
  setOrganizations: (organizations: organization[]) => void;
}) {
  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        const response = await apiClient.get("/organization");
        setOrganizations(response.data.data);
      } catch (error) {}
    };

    fetchOrganizations();
  }, []);

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="rounded-2xl shadow-lg bg-white dark:bg-gray-900 overflow-hidden">
        <Table className="w-full">
          <TableHeader className="bg-gray-100 dark:bg-gray-800">
            <TableRow>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-3 py-4">
                S.N.
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-3 py-4">
                Organization Name
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-3 py-4">
                Balance
              </TableHead>
              <TableHead className="text-right text-gray-700 dark:text-gray-300 font-semibold text-lg px-10 py-4">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {organizations.map((organization, index) => (
              <OrganizationRow
                key={organization.id}
                index={index}
                organization={organization}
                onDelete={onDelete}
                balance={organization.balance}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

// organization Row Component
function OrganizationRow({
  organization,
  index,
  onDelete,
}: {
  organization: organization;
  index: number;
  onDelete: (name: string) => void;
}) {
  const router = useRouter();
  const [showBalanceModal, setShowBalanceModal] = useState(false);

  return (
    <TableRow>
      <TableCell>{index + 1}</TableCell>
      <TableCell>{organization.name}</TableCell>
      <TableCell>{organization.balance}</TableCell>
      <TableCell className="text-right space-x-2">
        <Button
          variant="outline"
          size="ss"
          onClick={() => {
            sessionStorage.setItem("name", JSON.stringify(organization.name));
            router.push(`/app/settings/organizations/edit/${organization.id}`);
          }}
          className="flex items-center gap-1"
        >
          <SquarePen className="h-3 w-3 " />
        </Button>
        <DeleteOrganizationDialog
          organization={organization}
          onDelete={onDelete}
        />
        <Button
          variant="outline"
          size="ss"
          className="bg-green-600 text-white"
          onClick={() => setShowBalanceModal(true)}
        >
          <SquareAdd className="h-3 w-3 " />
        </Button>
        {showBalanceModal && (
          <AddOrganizationBalanceFormModal
            organization={organization}
            onClose={() => setShowBalanceModal(false)}
          />
        )}
      </TableCell>
    </TableRow>
  );
}

// Add organization Form Component
export function AddOrganizationForm({
  onSubmit,
}: {
  onSubmit: (organization: organization) => void;
}) {
  const [neworganization, setNeworganization] = useState({
    name: "",
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          id: Date.now(),
          ...neworganization,
          name: neworganization.name.trim(),
        });
        setNeworganization({
          name: "",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="Organization name"
        value={neworganization.name}
        onChange={(e) =>
          setNeworganization((prev) => ({
            ...prev,
            name: e.target.value,
          }))
        }
      />

      <Button type="submit" className="w-full">
        Add organization
      </Button>
    </form>
  );
}

function AddOrganizationBalanceFormModal({
  organization,
  onClose,
}: {
  organization: organization;
  onClose: () => void;
}) {
  const [amount, setAmount] = useState("");
  const [loading, setLoading] = useState(false);

  const handleAddBalance = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await apiClient.post(`/organization/topup/${organization.id}`, {
        balance: Number(amount),
      });
      alert("Balance added successfully!");
      setAmount("");
      onClose();
      window.location.reload();
    } catch (error) {
      alert("Failed to add balance");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded shadow-lg min-w-[300px]">
        <h2 className="text-lg font-bold mb-4">
          Add Balance to {organization.name}
        </h2>
        <form onSubmit={handleAddBalance} className="space-y-4">
          <Input
            type="number"
            placeholder="Enter amount"
            value={amount}
            min={0}
            onChange={(e) => setAmount(e.target.value)}
            required
          />
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "Adding..." : "Add Balance"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
