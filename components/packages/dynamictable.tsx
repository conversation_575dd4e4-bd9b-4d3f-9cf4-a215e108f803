"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { SquarePen, Trash2 } from "lucide-react";

export interface PackageDuration {
  month: string;
  price: number;
}

export interface Package {
  id: number;
  name: string;
  upload: string;
  download: string;
  type: string;
  durations: PackageDuration[];
}

export interface FlattenedPackage extends Omit<Package, "durations"> {
  duration: string;
  price: number;
}

type DynamicTableProps = {
  tablename: string;
  packages: FlattenedPackage[];
  onEdit?: (id: number) => void;
  // onDelete?: (id: number) => void;
  showActions?: boolean;
};

// Utility function to group packages by name
export const groupPackagesByName = (
  packages: FlattenedPackage[]
): Record<string, FlattenedPackage[]> => {
  return packages.reduce(
    (acc: Record<string, FlattenedPackage[]>, pkg: FlattenedPackage) => {
      if (!acc[pkg.name]) {
        acc[pkg.name] = [];
      }
      acc[pkg.name].push(pkg);
      return acc;
    },
    {}
  );
};

export default function DynamicTable({
  tablename,
  packages,
  onEdit,
  // onDelete,
  showActions = true,
}: DynamicTableProps) {
  if (!packages || packages.length === 0) {
    return (
      <div className="overflow-x-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">{tablename}</h2>
        </div>
        <div className="text-center py-8 text-gray-500 border rounded bg-gray-50">
          No packages found for {tablename}.
        </div>
      </div>
    );
  }

  // Define custom column headers and their display order
  const columnConfig = [
    {
      key: "id",
      label: "SN",
      render: (_value: any, index: number) => index + 1,
    },
    // { key: "name", label: "Package Name" },
    { key: "duration", label: "Duration (Months)" },
    { key: "upload", label: "Upload Speed" },
    { key: "download", label: "Download Speed" },
    { key: "price", label: "Price", render: (value: any) => `Rs. ${value}` },
  ];

  return (
    <div className="overflow-x-auto mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">{tablename}</h2>
        <div className="text-sm text-gray-500">
          {packages.length} package{packages.length !== 1 ? "s" : ""}
        </div>
      </div>

      <table className="min-w-full border text-sm">
        <thead className="bg-gray-100 text-left">
          <tr>
            {columnConfig.map((column) => (
              <th key={column.key} className="border px-4 py-2 font-semibold">
                {column.label}
              </th>
            ))}
            {showActions && (
              <th className="border px-4 py-2 font-semibold">Actions</th>
            )}
          </tr>
        </thead>
        <tbody>
          {packages.map((pkg, index) => (
            <tr key={`${pkg.id}-${pkg.duration}`} className="hover:bg-gray-50">
              {columnConfig.map((column) => (
                <td key={column.key} className="border px-4 py-2">
                  {column.render
                    ? column.render(
                        pkg[column.key as keyof FlattenedPackage],
                        index
                      )
                    : pkg[column.key as keyof FlattenedPackage]}
                </td>
              ))}
              {showActions && (
                <td className="border px-4 py-2">
                  <div className="flex gap-2">
                    {onEdit && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEdit(pkg.id)}
                        className="text-blue-600 hover:text-blue-700 flex items-center gap-1"
                      >
                        <SquarePen className="h-3 w-3 " />
                      </Button>
                    )}
                  </div>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
