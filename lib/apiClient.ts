import axios, { AxiosInstance, AxiosRequestConfig, AxiosError } from "axios";
import Cookies from "js-cookie";

// Types
interface JWTPayload {
  exp: number;
  [key: string]: any;
}

interface TokenResponse {
  accessToken: string;
  refreshToken?: string;
}

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BITFLUX_API_BASE_URL,
  timeout: 10000, // 10 seconds
});

// Global refresh state to prevent multiple simultaneous refresh attempts
let isRefreshing = false;
let refreshPromise: Promise<boolean> | null = null;

// Token management utilities
const TokenManager = {
  getAccessToken: (): string | undefined => {
    return Cookies.get("accessToken");
  },

  getRefreshToken: (): string | undefined => {
    return Cookies.get("refreshToken");
  },

  setTokens: (tokens: TokenResponse): void => {
    if (tokens.accessToken) {
      Cookies.set("accessToken", tokens.accessToken, {
        expires: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes
        path: "/",
        sameSite: "Strict",
        secure: window.location.protocol === "https:"
      });
    }

    if (tokens.refreshToken) {
      Cookies.set("refreshToken", tokens.refreshToken, {
        expires: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days
        path: "/",
        sameSite: "Strict",
        secure: window.location.protocol === "https:"
      });
    }
  },

  clearTokens: (): void => {
    Cookies.remove("accessToken", { path: "/" });
    Cookies.remove("refreshToken", { path: "/" });
  },

  decodeToken: (token: string): JWTPayload | null => {
    try {
      return JSON.parse(atob(token.split(".")[1])) as JWTPayload;
    } catch (error) {
      console.error("Error decoding token:", error);
      return null;
    }
  },

  isTokenValid: (token: string): boolean => {
    const payload = TokenManager.decodeToken(token);
    if (!payload) return false;

    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();

    // Token is valid if it has more than 1 minute remaining
    return expirationTime - currentTime > 60 * 1000;
  },

  // Check if token needs refresh (less than 5 minutes remaining)
  shouldRefreshToken: (token: string): boolean => {
    const payload = TokenManager.decodeToken(token);
    if (!payload) return true;

    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();

    // Refresh if token expires in less than 5 minutes
    return expirationTime - currentTime < 5 * 60 * 1000;
  },

  // Refresh access token using refresh token
  refreshAccessToken: async (): Promise<boolean> => {
    // If already refreshing, wait for that to complete
    if (isRefreshing && refreshPromise) {
      return refreshPromise;
    }

    const refreshToken = TokenManager.getRefreshToken();
    if (!refreshToken) {
      console.log("No refresh token available");
      return false;
    }

    // Check if refresh token is still valid
    if (!TokenManager.isTokenValid(refreshToken)) {
      console.log("Refresh token is expired");
      TokenManager.clearTokens();
      return false;
    }

    isRefreshing = true;

    refreshPromise = (async (): Promise<boolean> => {
      try {
        console.log("Refreshing access token...");

        const response = await axios.post<TokenResponse>(
          `${process.env.NEXT_PUBLIC_BITFLUX_API_BASE_URL}/token`,
          { refreshToken },
          { timeout: 10000 }
        );

        if (response.data.accessToken) {
          TokenManager.setTokens({
            accessToken: response.data.accessToken,
            refreshToken: response.data.refreshToken || refreshToken
          });
          console.log("Access token refreshed successfully");
          return true;
        } else {
          console.log("No access token in refresh response");
          return false;
        }
      } catch (error: any) {
        console.error("Token refresh failed:", error);

        // If refresh fails with 401/403, clear all tokens
        if (error.response?.status === 401 || error.response?.status === 403) {
          TokenManager.clearTokens();
        }

        return false;
      } finally {
        isRefreshing = false;
        refreshPromise = null;
      }
    })();

    return refreshPromise;
  }
};

// Request interceptor - Check and refresh token before each request
apiClient.interceptors.request.use(
  async (config: AxiosRequestConfig): Promise<AxiosRequestConfig> => {
    // Skip token validation for token refresh requests
    if (config.url?.includes("/token") || config.url?.includes("/login")) {
      return config;
    }

    const accessToken = TokenManager.getAccessToken();

    // If no access token, let the request proceed (it will likely fail with 401)
    if (!accessToken) {
      console.log("No access token available for request");
      return config;
    }

    // Check if access token is valid
    if (TokenManager.isTokenValid(accessToken)) {
      // Token is valid, use it
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${accessToken}`;
      return config;
    }

    // Access token is expired/invalid, try to refresh
    console.log("Access token is invalid, attempting refresh");
    const refreshSuccess = await TokenManager.refreshAccessToken();

    if (refreshSuccess) {
      const newAccessToken = TokenManager.getAccessToken();
      if (newAccessToken) {
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${newAccessToken}`;
        console.log("Using refreshed access token for request");
        return config;
      }
    }

    // If refresh failed, clear tokens and let request proceed without auth
    console.log("Token refresh failed, proceeding without authorization");
    TokenManager.clearTokens();

    return config;
  },
  (error: AxiosError) => Promise.reject(error)
);

// Response interceptor - Handle 401 errors
apiClient.interceptors.response.use(
  (response: any) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // Handle 401 errors by attempting token refresh
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !originalRequest.url?.includes("/token") &&
      !originalRequest.url?.includes("/login")
    ) {
      originalRequest._retry = true;

      console.log("Received 401 error, attempting token refresh");
      const refreshSuccess = await TokenManager.refreshAccessToken();

      if (refreshSuccess) {
        const newAccessToken = TokenManager.getAccessToken();
        if (newAccessToken) {
          originalRequest.headers = originalRequest.headers || {};
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
          console.log("Retrying request with refreshed token");
          return apiClient(originalRequest);
        }
      }

      // If refresh failed, clear tokens
      console.log("Token refresh failed after 401, clearing tokens");
      TokenManager.clearTokens();
    }

    return Promise.reject(error);
  }
);

// Export the enhanced API client and token manager
export default apiClient;
export { TokenManager };
