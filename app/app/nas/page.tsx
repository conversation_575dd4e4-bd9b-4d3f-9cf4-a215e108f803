"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import apiClient from "@/lib/apiClient";
import Link from "next/link";
import { NAS } from "@/types/nas";
import { Square<PERSON>en, Trash2 } from "lucide-react";

const NasPage = () => {
  const router = useRouter();
  const [nasData, setNasData] = useState<NAS[]>([]);

  useEffect(() => {
    const fetchNas = async () => {
      try {
        const res = await apiClient.get("/nas");
        const data = res.data.data;
        setNasData(data);
      } catch (error) {
        console.error("Error fetching NAS data:", error);
      }
    };
    fetchNas();
  }, []);

  const handleDeleteNAS = async (id: string, nasname: string) => {
    if (
      window.confirm(
        `Are you sure you want to delete NAS "${nasname}"? This action cannot be undone.`
      )
    ) {
      try {
        await apiClient.delete(`/nas/${id}`);
        setNasData(nasData.filter((nas) => nas.id !== id));
        console.log("NAS deleted successfully");
      } catch (error) {
        console.error("Failed to delete NAS:", error);
        alert("Failed to delete NAS. Please try again.");
      }
    }
  };

  const handleEditNAS = (nas: NAS) => {
    // Store NAS data in sessionStorage for the edit page
    sessionStorage.setItem("editNAS", JSON.stringify(nas));
    router.push(`/app/nas/edit/${nas.id}`);
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">NAS</h1>
        <Link href="/app/nas/add">
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded shadow">
            + Add NAS
          </button>
        </Link>
      </div>
      <div className="bg-white rounded shadow p-4">
        <table className="min-w-full table-auto">
          <thead>
            <tr className="bg-gray-100">
              <th className="px-4 py-2 text-left">SN</th>
              <th className="px-4 py-2 text-left">Vendor</th>
              <th className="px-4 py-2 text-left">NAS Name</th>
              <th className="px-4 py-2 text-left">Description</th>
              <th className="px-4 py-2 text-left">Action</th>
            </tr>
          </thead>
          <tbody>
            {nasData && nasData.length > 0 ? (
              nasData.map((item: NAS, idx: number) => (
                <tr key={item.id || idx} className="border-b hover:bg-gray-50">
                  <td className="px-4 py-2">{idx + 1}</td>
                  <td className="px-4 py-2">{item.vendor}</td>
                  <td className="px-4 py-2">{item.nasname}</td>
                  <td className="px-4 py-2">{item.description}</td>
                  <td className="px-4 py-2 flex gap-2">
                    <button
                      className="bg-yellow-400 hover:bg-yellow-500 text-white px-3 py-1 rounded text-sm flex items-center gap-1"
                      onClick={() => handleEditNAS(item)}
                    >
                      <SquarePen className="h-3 w-3" />
                    </button>
                    <button
                      className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm flex items-center gap-1"
                      onClick={() => handleDeleteNAS(item.id!, item.nasname)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="text-center py-6 text-gray-500">
                  No NAS data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default NasPage;
